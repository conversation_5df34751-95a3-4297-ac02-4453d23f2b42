<script setup lang="ts">
import ImgUpload from "@/views/createProject/components/imgUpload.vue";
import TitleForm from "@/views/createProject/components/titleForm.vue";

import {
  ref,
  reactive,
  onMounted,
  onBeforeMount,
  computed,
  onUnmounted,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { editingPrjStore } from "@/stores/editingPrj";
import MySteps from "@/views/createProject/components/mySteps.vue";
import MyButton from "@/views/common/myButton.vue";
// import ckEditor from "@/views/common/inlineCKEditor.vue";
// import ckEditorOnlyMath from "@/views/common/InlineCKEditorOnlyMath.vue";
import InlineEditor from "@/components/editors/VeditorInline.vue";
import { prjForm, typeDict_noDefault, typeDict } from "@/utils/constant";
import { ElMessage } from "element-plus";
import {
  getProjectDetail,
  type params4saveDraft,
  saveDraftAPI,
  createProject,
} from "@/apis/path/createProject";

import type { FormInstance, FormRules } from "element-plus";
import type { prjInfoType, tagType } from "@/utils/type";
import { formatData_step1, checkCoverUrl, findKeyByValue } from "@/utils/func";

const router = useRouter();
const route = useRoute();
const editingPrj = editingPrjStore();
const curPrjForm = ref();
const prjId = ref();
const ruleFormRef = ref<FormInstance>();
// 初始时没有按钮被激活
const activeIndex = ref("-1");
const prjTypeFlag = computed(() => formData.prjType !== "3");
// formData用于和后端通信
const formData = reactive<prjInfoType>({
  disable: false,
  prjType: "",
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjAreaList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
});
const nameEditor = ref();
const aimEditor = ref();
const generalEditor = ref();
const disableType = ref(false);
// 如果trigger设置成change，初始化时会触发一次验证
const rules4klgAndExam = reactive<FormRules<prjInfoType>>({
  prjType: [{ required: true, message: "请选择项目类型", trigger: "blur" }],
  prjName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  // prjAim: [{ required: prjTypeFlag.value,  message: '请输入项目目标', trigger: 'blur' }],
  // prjGeneral: [{ required: prjTypeFlag.value , message: '请输入项目简介', trigger: 'blur' }],
  // prjTagList: [{ required: true, message: '请输入项目标签', trigger: 'blur' }],
  // prjTargetList: [
  //   { required: true, message: "请输入项目目标", trigger: "blur" },
  // ],
  prjCover: [{ validator: checkCoverUrl, trigger: "change" }],
});
const rules4prj = reactive<FormRules<prjInfoType>>({
  prjType: [{ required: true, message: "请选择项目类型", trigger: "blur" }],
  prjName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  // prjAim: [{ required: true, message: "请输入项目目标", trigger: "blur" }],
  // prjGeneral: [{ required: true, message: "请输入项目简介", trigger: "blur" }],
  // prjTagList: [{ required: true, message: '请输入项目标签', trigger: 'blur' }],
  // prjTargetList: [
  //   { required: true, message: "请输入项目目标", trigger: "blur" },
  // ],
  prjCover: [{ validator: checkCoverUrl, trigger: "change" }],
});
const rules4area = reactive<FormRules<prjInfoType>>({
  prjType: [{ required: true, message: "请选择项目类型", trigger: "blur" }],
  prjName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  // prjAim: [{ required: true, message: "请输入项目目标", trigger: "blur" }],
  // prjGeneral: [{ required: true, message: "请输入项目概述", trigger: "blur" }],
  // prjAreaList: [{ required: true, message: "请添加领域", trigger: "blur" }],
  prjCover: [{ validator: checkCoverUrl, trigger: "change" }],
});
const rules = computed(() => {
  switch (formData.prjType) {
    case "1":
      return rules4klgAndExam;
    case "2":
      return rules4prj;
    case "3":
      return rules4klgAndExam;
    case "4":
      return rules4area;
    default:
      return rules4klgAndExam;
  }
});

const receiveCover = (CommUrl: string, EchoUrl: string) => {
  // commUrl: 前后端通信时的图片链接(short)
  // echoUrl: 前端展示的图片链接(long)
  const newCover = {
    echoUrl: EchoUrl,
    commUrl: CommUrl,
  };
  formData.prjCover = newCover; // 和后端的通信结构
  editingPrj.setPrjCover(newCover); // 前端暂存
};
const receivePrjId = (projectId: number) => {
  prjId.value = projectId;
  editingPrj.setPrjId(prjId.value);
  const currentUrl = route.fullPath;
  const url = new URL(currentUrl, window.location.origin);
  router.replace({
    path: url.pathname,
    query: {
      prjId: prjId.value,
      prjForm: curPrjForm.value,
    },
  });
};
const handleClose = () => {
  window.close();
};
const saveDraft = (isNext: boolean) => {
  const param: params4saveDraft = {
    oid: prjId.value,
    // 这个地方，后端直接要的字符串，like"知识讲解"
    prjType: findKeyByValue(formData.prjType, typeDict),
    prjForm: curPrjForm.value,
    title: formData.prjName,
    description: formData.prjGeneral,
    coverPic: formData.prjCover.commUrl,
    purpose: formData.prjAim,
    prjTag: formData.prjTagList?.map((i: tagType) => parseInt(i.id)),
    targetKlg: [],
    status: isNext ? 1 : 0, // 草稿
  };
  switch (formData.prjType) {
    case "4":
      param.targetKlg = formData.prjAreaList?.map((i: tagType) => i.id);
      break;
    default:
      param.targetKlg = formData.prjTargetList?.map((i: tagType) => i.id);
      break;
  }
  saveDraftAPI(param)
    .then((res) => {
      if (res.success) {
        // ElMessage.success(res.message);
        if (isNext) {
          router.push({
            path: "/home/<USER>/step2",
            query: {
              prjId: prjId.value,
            },
          });
        } else {
          ElMessage.success("保存草稿成功");
        }
        return true;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
const handleSaveDraft = () => {
  ruleFormRef.value?.validateField("prjName", (valid) => {
    if (valid) {
      if (!prjId.value) {
        createProject(curPrjForm.value).then((res) => {
          if (res.success) {
            receivePrjId(res.data.projectID);
            // prjId.value = res.data.projectID
            // editingPrj.setPrjId(prjId.value)
            saveDraft(false);
          } else {
            ElMessage.error(res.message);
            return;
          }
        });
      } else {
        saveDraft(false);
      }
    }
  });
};
const handleNextStep = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (!prjId.value) {
        // 其实已经不太可能进来了
        createProject(curPrjForm.value).then((res) => {
          if (res.success) {
            prjId.value = res.data.projectID;
            saveDraft(true);
          } else {
            ElMessage.error(res.message);
          }
        });
      } else {
        saveDraft(true);
      }
    } else {
      console.log("表单校验失败");
    }
  });
};
onBeforeMount(() => {
  curPrjForm.value = route.query.prjForm;
  editingPrj.setForm(curPrjForm.value);
  if (route.query.prjId) {
    // 后两步跳转过来的，或者编辑跳过来的
    prjId.value = parseInt(route.query.prjId as string);
  }
  // if (editingPrj.getPrjId()) {
  //   // 上传图片的组件有创建项目，然后刷新页面了
  // prjId.value = editingPrj.getPrjId()
  // }
});

// 激活按钮样式
const active = computed(() => {
  return "state_" + activeIndex.value;
});
// 设置激活按钮
const setActiveType = (type: any) => {
  activeIndex.value = type;
  formData.prjType = type;
};

const getProject = () => {
  if (prjId.value) {
    // 编辑模式：先用后端数据初始化
    getProjectDetail(prjId.value)
      .then((res) => {
        if (res.success) {
          let baseData = res.data.list[0];
          // 这个部分后端传来的和接受的都是typeName，前端存储都转换成了typeCode
          // 因为整个后端没有统一传输到底是code还是name，所以前端自己统一一下数据便于管理
          // 在formatData_step1中，把type字段转化成typeName
          Object.assign(formData, reactive(formatData_step1(baseData)));
          // curPrjForm.value = 1;
          // editingPrj.setForm(parseInt(curPrjForm.value));
          // 如果前端有持久化的数据,覆盖写入（因为刷新）
          let sessionData: prjInfoType = editingPrj.getPrjDetail();
          for (const key of Object.keys(sessionData)) {
            if (key == "prjTagList" || key == "prjTargetList") {
              if (sessionData[key].length !== 0) {
                formData[key] = sessionData[key];
              }
            } else if (key == "prjCover") {
              if (
                sessionData[key].echoUrl != "" &&
                sessionData[key].commUrl != ""
              ) {
                formData[key] = sessionData[key];
              }
            } else if (sessionData[key] != "") {
              formData[key] = sessionData[key];
              console.log("闹鬼了，怎么重写了没暂存过的数据: " + key);
            }
          }
          // 此处取数据维护的是formData，但是封面显示的url是从editingPrj里取的
          // 所以维护一下editingPrj
          disableType.value = formData.disable;
          editingPrj.setPrjCover(formData.prjCover);
          // nameEditor.value.setData(formData.prjName)
          // aimEditor.value.setData(formData.prjAim)
          // generalEditor.value.setData(formData.prjGeneral)
          activeIndex.value = formData.prjType;
          editingPrj.setPrjTagList(formData.prjTagList);
          editingPrj.setPrjTargetList(formData.prjTargetList);
        } else {
          ElMessage.error("初始化失败");
          router.push(`/home/<USER>
        }
      })
      .catch();
  }
};
onMounted(() => {
  getProject();
});
</script>

<template>
  <my-steps :action="0" :form="curPrjForm"></my-steps>
  <div class="main-container">
    <title-form :form="curPrjForm"></title-form>
    <div class="line"></div>
    <div class="content-wrapper">
      <el-form
        ref="ruleFormRef"
        :model="formData"
        :rules="rules"
        @submit.prevent
      >
        <div class="floor">
          <span></span>
          <span class="left-container">
            <el-form-item
              class="type"
              label="项目类型"
              prop="prjType"
              label-width="70px"
            >
              <span class="btn-box">
                <!-- 完全不要知识测评类 -->
                <el-button
                  v-model="formData.prjType"
                  class="type-btn"
                  v-for="[index, type] in Object.entries(
                    typeDict_noDefault
                  ).filter(([key]) => key !== '知识测评')"
                  :disabled="disableType"
                  :key="index"
                  :label="index"
                  :value="type"
                  :class="type === activeIndex ? active : 'state_0'"
                  @click="setActiveType(type)"
                >
                  {{ index }}
                </el-button>
              </span>
            </el-form-item>
            <el-form-item
              class="name"
              label="项目名称"
              prop="prjName"
              label-width="70px"
            >
              <InlineEditor
                ref="nameEditor"
                v-model="formData.prjName"
                class="input"
                :height="40"
              ></InlineEditor>
            </el-form-item>
            <el-form-item
              class="aim"
              label="项目目标"
              prop="prjAim"
              label-width="70px"
            >
              <InlineEditor
                ref="aimEditor"
                v-model="formData.prjAim"
                :showToolbar="true"
                :height="40"
                class="input"
                :auto-height="true"
                :max-height="95"
              ></InlineEditor>
            </el-form-item>
          </span>
          <span class="right-container">
            <el-form-item prop="prjCover">
              <img-upload
                @send-img-url="receiveCover"
                @sendPrjId="receivePrjId"
                :projectForm="curPrjForm"
                :PrjId="prjId"
                :LongUrl="formData.prjCover.echoUrl"
              ></img-upload>
              <span class="tip"
                >项目封面格式为jpeg,png,文件大小不超过2MB,建议长宽比4:3。</span
              >
            </el-form-item>
          </span>
        </div>
        <div class="floor">
          <el-form-item
            class="general"
            label="项目概述"
            prop="prjGeneral"
            label-width="70px"
          >
            <InlineEditor
              ref="generalEditor"
              v-model="formData.prjGeneral"
              :height="40"
              :showToolbar="true"
              :auto-height="true"
              :max-height="200"
              class="input"
            ></InlineEditor>
          </el-form-item>
        </div>
        <!-- <div class="floor" style="margin-bottom: 40px;">
          <el-form-item label="项目标签" prop="prjTagList">
            <my-button @click="handleAddTag" style="margin-left: 40px;">+ 添加标签</my-button>
            <div class="selectedWrapper" style="width: 830px;">
              <my-tag class="t" v-for="tag in formData.prjTagList" :tag-id="tag.id" :key="tag.id" @delete="handleDeleteTag(tag.id)" type="tag">
                <el-tooltip
                    popper-class="tooltip-width"
                    :content="tag.name"
                    raw-content
                >
                <span v-html="tag.name"></span>
                </el-tooltip>
              </my-tag>
            </div>
          </el-form-item>
        </div> -->
      </el-form>

      <div class="tool-bar">
        <my-button type="light" @click="handleClose()">关闭</my-button>
        <my-button @click="handleSaveDraft()">存草稿</my-button>
        <my-button @click="handleNextStep()">下一步</my-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.form {
  padding-left: 30px;
  margin-right: 322px;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-primary);
}
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  margin: 10px auto auto auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  :deep(.el-form-item__content) {
    align-items: flex-start;
  }
  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 30px 55px;
    :deep(.el-form-item__label) {
      padding: 0;
    }
    .floor {
      display: flex;
      width: 100%;
      align-items: flex-start;
      .selectedWrapper {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        margin-left: 30px;
        flex-wrap: wrap;
        .t {
          margin-right: 30px;
          margin-bottom: 10px;
        }
      }

      .left-container {
        display: flex;
        flex-direction: column;
        width: 826px;
        margin-right: 20px;
        flex-shrink: 0;

        :deep(.input) {
          width: 735px !important;
          max-width: 100% !important;
          box-sizing: border-box !important;
          display: block;
          display: flex;
        }
        .type {
          /*--el-color-primary: var(--color-primary);*/
          :deep(.el-input) {
            --el-input-height: 35px;
          }
        }
      }

      :deep(.input) {
        margin-left: 30px;
      }

      .general {
        width: 100% !important;
        flex-shrink: 0;
        margin-top: 5px;
        :deep(.el-form-item__content) {
          width: calc(100% - 70px) !important;
          margin-left: 30px !important; /* 恢复水平间距 */

          .input {
            width: 100% !important;
            margin-left: 0 !important; /* 移除input的margin-left，因为已经在content上设置了 */
          }
        }
      }
      .right-container {
        :deep(.el-upload--picture-card) {
          width: 239px;
        }
        .tip {
          white-space: nowrap;
          font-size: 10px;
          color: var(--color-deep);
        }
      }
      .additional_block {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 10px;
        background-color: var(--color-light);
        .title {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
    .tool-bar {
      width: 420px;
      display: flex;
      justify-content: space-between;
    }
  }
}
.btn-box {
  margin-left: 30px;

  .type-btn {
    width: 80px;
    height: 20px;
    cursor: pointer;
    padding: 0 10px 0 10px;
    font-size: 14px;
    text-align: center;
  }
  .el-button {
    background-color: white;
  }
  .el-button:hover {
    font-weight: bold;
  }
  .state_0 {
    border-color: var(--color-group-background);
    color: var(--color-group-background);
  }
  .state_1 {
    border-color: var(--color-case-btn);
    color: var(--color-case-btn);
  }
  .state_2 {
    border-color: var(--color-explain-btn);
    color: var(--color-explain-btn);
  }
  .state_3 {
    border-color: var(--color-test-btn);
    color: var(--color-test-btn);
  }
  .state_4 {
    border-color: var(--color-primary);
    color: var(--color-primary);
  }
}
</style>
